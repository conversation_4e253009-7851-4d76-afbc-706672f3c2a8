<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;


class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'messages';

    protected $fillable = [
        'message_id',
        'profile_id',
        'type',
        'content',
        'is_outgoing',
        'is_read',
        'is_deleted',
        'is_delivered',
        'sent_at',
    ];

    protected $casts = [
        'is_outgoing' => 'boolean',
        'is_read' => 'boolean',
        'is_delivered' => 'boolean',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the profile that owns this message
     */
    public function profile(): BelongsTo
    {
        return $this->belongsTo(WhatsAppProfile::class, 'profile_id');
    }

    /**
     * Get all attachments for this message
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(WhatsAppAttachment::class, 'message_id');
    }



    /**
     * Scope for incoming messages
     */
    public function scopeIncoming($query)
    {
        return $query->where('is_outgoing', false);
    }

    /**
     * Scope for outgoing messages
     */
    public function scopeOutgoing($query)
    {
        return $query->where('is_outgoing', true);
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for delivered messages
     */
    public function scopeDelivered($query)
    {
        return $query->where('is_delivered', true);
    }
}
