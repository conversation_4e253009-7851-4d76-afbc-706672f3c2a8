<?php

namespace App\Livewire\SocialBots;

use Livewire\Component;
use App\Models\WhatsApp\{WhatsAppProfile, WhatsAppMessage, WhatsAppSettings};
use App\Events\WhatsAppMessaging;
use Illuminate\Support\Facades\{DB, Auth};
use Carbon\Carbon;

class Whatsapp extends Component
{
    public $conversations = [];
    public $selectedConversation = null;
    public $messages = [];
    public $newMessage = '';
    public $sendingMessage = false;

    public function mount()
    {
        $this->loadConversations();
    }

    public function loadConversations()
    {
        $connectedUser = $this->getConnectedUser();
        if (!$connectedUser) {
            $this->conversations = [];
            return;
        }

        $this->conversations = WhatsAppProfile::select([
            'profiles.*',
            'last_messages.content as last_message_content',
            'last_messages.type as last_message_type',
            'last_messages.sent_at as last_message_time',
            'last_messages.is_outgoing as last_message_is_outgoing',
            DB::raw('COUNT(CASE WHEN messages.is_read = 0 AND messages.is_outgoing = 0 THEN 1 END) as unread_count')
        ])
            ->where('profiles.connected_user_id', $connectedUser->id)
            ->leftJoin('messages', 'profiles.id', '=', 'messages.profile_id')
            ->leftJoin(
                'messages as last_messages',
                fn($join) =>
                $join->on('profiles.id', '=', 'last_messages.profile_id')
                    ->whereRaw('last_messages.id = (SELECT MAX(id) FROM messages WHERE messages.profile_id = profiles.id)')
            )
            ->groupBy([
                'profiles.id',
                'profiles.whatsapp_id',
                'profiles.name',
                'profiles.phone_number',
                'profiles.profile_picture',
                'last_messages.content',
                'last_messages.type',
                'last_messages.sent_at',
                'last_messages.is_outgoing'
            ])
            ->orderBy('last_messages.sent_at', 'desc')
            ->get()
            ->map(fn($c) => $this->formatConversation($c))
            ->toArray();
    }

    public function loadConversationMessages($profileId)
    {
        $connectedUser = $this->getConnectedUser();
        if (!$connectedUser) return;

        $profile = WhatsAppProfile::where('id', $profileId)
            ->where('connected_user_id', $connectedUser->id)
            ->firstOrFail();

        $this->selectedConversation = [
            'id' => $profile->id,
            'name' => $profile->name ?: $profile->phone_number,
            'phone_number' => $profile->phone_number,
            'profile_picture' => $profile->profile_picture ?: asset('images/200x200.png'),
            'is_business' => $profile->is_business,
            'is_group' => $profile->is_group,
            'whatsapp_id' => $profile->whatsapp_id
        ];

        $this->messages = WhatsAppMessage::where('profile_id', $profileId)
            ->with('attachments')
            ->orderBy('sent_at', 'asc')
            ->get()
            ->map(fn($m) => $this->formatMessage($m))
            ->toArray();

        // Mark as read and refresh conversations
        WhatsAppMessage::where('profile_id', $profileId)
            ->where('is_outgoing', false)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        $this->loadConversations();
    }

    public function sendMessage()
    {
        if (!$this->selectedConversation || !trim($this->newMessage)) return;

        $this->sendingMessage = true;

        try {
            $message = WhatsAppMessage::create([
                'message_id' => 'msg_' . time() . '_' . rand(1000, 9999),
                'profile_id' => $this->selectedConversation['id'],
                'type' => 'text',
                'content' => trim($this->newMessage),
                'is_outgoing' => true,
                'is_read' => true,
                'sent_at' => now(),
            ]);

            $this->messages[] = $this->formatMessage($message);
            $this->newMessage = '';
            $this->loadConversations();

            broadcast(new WhatsAppMessaging(Auth::id(), $this->selectedConversation['id'], 'message_received'));
            session()->flash('success', 'Message sent successfully');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to send message: ' . $e->getMessage());
        }

        $this->sendingMessage = false;
    }

    public function refreshMessages($profileId)
    {
        if (!$this->validateProfileAccess($profileId)) return;

        if ($this->selectedConversation['id'] == $profileId) {
            $this->loadConversationMessages($profileId);
        } else {
            $this->updateSingleConversation($profileId);
        }
    }

    public function refreshMessageStatuses($profileId)
    {
        $this->refreshMessages($profileId);
    }

    public function getConversationAttachmentsProperty()
    {
        return collect($this->messages)
            ->pluck('attachments')
            ->flatten(1)
            ->filter();
    }

    private function getConnectedUser()
    {
        return WhatsAppSettings::where('user_id', Auth::id())->first()?->connectedUser;
    }

    private function validateProfileAccess($profileId)
    {
        $connectedUser = $this->getConnectedUser();
        return $connectedUser && WhatsAppProfile::where('id', $profileId)
            ->where('connected_user_id', $connectedUser->id)
            ->exists();
    }

    private function formatConversation($conversation)
    {
        return [
            'id' => $conversation->id,
            'whatsapp_id' => $conversation->whatsapp_id,
            'name' => $conversation->name ?: $conversation->phone_number,
            'phone_number' => $conversation->phone_number,
            'profile_picture' => $conversation->profile_picture ?: asset('images/200x200.png'),
            'last_message_content' => $conversation->last_message_content,
            'last_message_type' => $conversation->last_message_type,
            'last_message_time' => $conversation->last_message_time ?
                Carbon::parse($conversation->last_message_time)->format('H:i') : '',
            'last_message_is_outgoing' => $conversation->last_message_is_outgoing,
            'unread_count' => $conversation->unread_count
        ];
    }

    private function formatMessage($message)
    {
        return [
            'id' => $message->id,
            'message_id' => $message->message_id,
            'type' => $message->type,
            'content' => $message->content,
            'attachments' => $message->attachments?->map(fn($a) => [
                'id' => $a->id,
                'filename' => $a->filename,
                'mime_type' => $a->mime_type,
                'file_size' => $a->file_size,
                'file_url' => $a->file_url,
                'whatsapp_media_url' => $a->file_url,
                'is_image' => $a->isImage(),
                'is_video' => $a->isVideo(),
                'is_audio' => $a->isAudio(),
                'is_document' => $a->isDocument(),
            ])->toArray() ?? [],
            'has_attachments' => $message->attachments?->count() > 0,
            'is_outgoing' => $message->is_outgoing,
            'is_read' => $message->is_read,
            'sent_at' => $message->sent_at,
            'formatted_time' => $message->sent_at->format('H:i'),
            'formatted_date' => $message->sent_at->format('Y-m-d')
        ];
    }

    private function updateSingleConversation($profileId)
    {
        try {
            $connectedUser = $this->getConnectedUser();
            $updatedConversation = WhatsAppProfile::select([
                'profiles.*',
                'last_messages.content as last_message_content',
                'last_messages.type as last_message_type',
                'last_messages.sent_at as last_message_time',
                'last_messages.is_outgoing as last_message_is_outgoing',
                DB::raw('COUNT(CASE WHEN messages.is_read = 0 AND messages.is_outgoing = 0 THEN 1 END) as unread_count')
            ])
                ->where('profiles.id', $profileId)
                ->where('profiles.connected_user_id', $connectedUser->id)
                ->leftJoin('messages', 'profiles.id', '=', 'messages.profile_id')
                ->leftJoin(
                    'messages as last_messages',
                    fn($join) =>
                    $join->on('profiles.id', '=', 'last_messages.profile_id')
                        ->whereRaw('last_messages.id = (SELECT MAX(id) FROM messages WHERE messages.profile_id = profiles.id)')
                )
                ->groupBy([
                    'profiles.id',
                    'profiles.whatsapp_id',
                    'profiles.name',
                    'profiles.phone_number',
                    'profiles.profile_picture',
                    'last_messages.content',
                    'last_messages.type',
                    'last_messages.sent_at',
                    'last_messages.is_outgoing'
                ])
                ->first();

            if ($updatedConversation) {
                $conversationData = $this->formatConversation($updatedConversation);
                $index = array_search($profileId, array_column($this->conversations, 'id'));

                if ($index !== false) {
                    $this->conversations[$index] = $conversationData;
                } else {
                    array_unshift($this->conversations, $conversationData);
                }

                usort(
                    $this->conversations,
                    fn($a, $b) =>
                    strtotime($b['last_message_time'] ?? '00:00') - strtotime($a['last_message_time'] ?? '00:00')
                );
            }
        } catch (\Exception $e) {
            $this->loadConversations();
        }
    }

    public function render()
    {
        return view('livewire.social-bots.whatsapp.whatsapp-main')->layout(
            'components.base-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Whatsapp Bot',
                'hasMinSidebar' => 'true'
            ]
        );
    }
}
