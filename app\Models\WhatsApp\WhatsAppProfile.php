<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppProfile extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'profiles';

    protected $fillable = [
        'connected_user_id',
        'whatsapp_id',
        'phone_number',
        'name',
        'profile_picture',
    ];

    /**
     * Get the connected user that owns this profile
     */
    public function connectedUser(): BelongsTo
    {
        return $this->belongsTo(WhatsAppConnectedUser::class, 'connected_user_id');
    }

    /**
     * Get all messages for this profile
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'profile_id');
    }

    /**
     * Get the latest message (replaces conversation table)
     */
    public function latestMessage()
    {
        return $this->messages()->latest('sent_at')->first();
    }

    /**
     * Get unread messages count
     */
    public function unreadCount(): int
    {
        return $this->messages()->where('is_outgoing', false)->where('is_read', false)->count();
    }
}
